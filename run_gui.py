#!/usr/bin/env python3
"""
财政收入预测分析系统启动脚本
自动检查依赖并启动GUI程序
"""

import sys
import subprocess
import importlib
import os

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误：需要Python 3.7或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    print(f"✅ Python版本检查通过：{sys.version}")
    return True

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = {
        'pandas': 'pandas',
        'sklearn': 'scikit-learn', 
        'matplotlib': 'matplotlib',
        'numpy': 'numpy',
        'openpyxl': 'openpyxl'
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            importlib.import_module(package)
            print(f"✅ {pip_name} 已安装")
        except ImportError:
            print(f"❌ {pip_name} 未安装")
            missing_packages.append(pip_name)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖包"""
    if not packages:
        return True
    
    print(f"\n🔧 正在安装缺失的依赖包：{', '.join(packages)}")
    
    try:
        for package in packages:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败：{e}")
        return False

def check_data_file():
    """检查数据文件是否存在"""
    data_file = 'finance_data.csv'
    if os.path.exists(data_file):
        print(f"✅ 数据文件存在：{data_file}")
        return True
    else:
        print(f"❌ 数据文件不存在：{data_file}")
        return False

def start_gui():
    """启动GUI程序"""
    try:
        print("\n🚀 启动财政收入预测分析系统...")
        from finance_prediction_gui import FinancePredictionGUI
        
        app = FinancePredictionGUI()
        app.run()
        
    except Exception as e:
        print(f"❌ 启动GUI失败：{e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🏛️  X市财政收入预测分析系统")
    print("=" * 60)
    print()
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    print()
    
    # 检查依赖
    print("🔍 检查依赖包...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n⚠️  发现缺失的依赖包：{', '.join(missing_packages)}")
        response = input("是否自动安装？(y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            if not install_dependencies(missing_packages):
                input("按回车键退出...")
                return
        else:
            print("请手动安装依赖包后再运行程序")
            print(f"安装命令：pip install {' '.join(missing_packages)}")
            input("按回车键退出...")
            return
    
    print()
    
    # 检查数据文件
    print("📁 检查数据文件...")
    if not check_data_file():
        print("请确保 finance_data.csv 文件在当前目录中")
        input("按回车键退出...")
        return
    
    print()
    
    # 启动GUI
    start_gui()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序运行出错：{e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
