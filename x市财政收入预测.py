import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt
import warnings

warnings.filterwarnings('ignore')    # 忽略警告提示

data = pd.read_csv('finance_data.csv', index_col=0)   # 读取数据
X = data.iloc[0:-2, 0:-1]    # 样本自变量
y = data.iloc[:-2, -1]       # 样本目标变量

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1)    # 数据拆分
lr = LinearRegression()             # 实例化得到一个线性回归分析模型对象
lr.fit(X_train, y_train)            # 模型训练
y_test_pre = lr.predict(X_test)     # 模型预测


# 定义平均绝对百分比误差函数
def mape(y_true, y_pre):
    return (abs((y_pre - y_true)/y_true)).mean()


mape(y_test, y_test_pre)                 # 模型的性能指标（在测试样本上的平均绝对百分比误差）

y_pre = lr.predict(data.iloc[:, :-1])    # 模型对所有样本的预测结果


plt.figure()     # 构建一张画布
plt.rcParams['font.sans-serif'] = 'SimHei'           # 用来正常显示中文标条
plt.plot(range(1, 21), data.iloc[:-2, -1], 'b-o')    # 实际样本分布
plt.plot(range(1, 23), y_pre, 'r-*')                 # 预测结果分布
plt.legend(['实际值', '预测值'])     # 添加图例
plt.grid('--')                     # 设置网格线
plt.show()

X_new = data.iloc[-2:, :-1]    # 未来两年（最后两个样本）的样本自变量
result = lr.predict(X_new)     # 未来两年G市的财政收入预测结果
