# X市财政收入预测分析系统

## 📊 项目简介

本项目是一个基于机器学习的财政收入预测分析系统，使用线性回归模型对X市的财政收入进行预测分析。项目提供了直观的GUI界面，让用户可以轻松进行数据分析和预测。

## ✨ 主要功能

- 📈 **数据可视化**：实际值与预测值对比图表
- 🤖 **智能预测**：基于历史数据预测未来财政收入
- 📊 **模型评估**：提供MAPE等性能指标
- 🎛️ **参数调节**：可调节测试集比例、预测年数等参数
- 💾 **数据管理**：支持CSV文件导入和结果导出
- 🖥️ **友好界面**：简洁美观的GUI操作界面

## 🛠️ 技术架构

### 核心技术栈
- **Python 3.x** - 主要编程语言
- **tkinter** - GUI界面框架
- **pandas** - 数据处理和分析
- **scikit-learn** - 机器学习模型
- **matplotlib** - 数据可视化

### 项目结构
```
├── README.md                    # 项目说明文档
├── run_gui.py                  # 一键启动脚本
├── finance_prediction_gui.py    # GUI主程序
├── finance_model.py            # 模型逻辑封装
├── x市财政收入预测.py           # 原始预测脚本
├── finance_data.csv            # 财政数据文件
├── 线性模型.py                 # 线性回归示例
├── 教程.txt                    # 运行教程
└── requirements.txt            # 依赖包列表
```

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 推荐使用虚拟环境

### 安装依赖
```bash
pip install pandas scikit-learn matplotlib
```

### 运行程序

#### 方式一：一键启动（推荐）
```bash
python run_gui.py
```
*自动检查依赖并启动GUI程序*

#### 方式二：直接启动GUI
```bash
python finance_prediction_gui.py
```

#### 方式三：命令行脚本
```bash
python x市财政收入预测.py
```

## 📋 使用说明

### GUI界面操作流程

1. **数据加载**
   - 点击"数据加载"按钮选择CSV文件
   - 系统自动验证数据格式和完整性

2. **参数设置**
   - 调节测试集比例（默认10%）
   - 设置预测年数（默认2年）

3. **模型训练**
   - 点击"模型训练"按钮开始训练
   - 查看训练进度和性能指标

4. **预测分析**
   - 点击"开始预测"进行未来预测
   - 查看预测结果和可视化图表

5. **结果导出**
   - 保存预测结果到Excel文件
   - 导出可视化图表

### 数据格式要求

CSV文件应包含以下列：
- `x1, x3, x4, x5, x6, x7, x8, x13` - 特征变量
- `y` - 目标变量（财政收入）

## 📊 模型说明

### 算法选择
使用**线性回归（Linear Regression）**算法，适合财政收入这类连续数值预测问题。

### 评估指标
- **MAPE（平均绝对百分比误差）**：衡量预测精度的主要指标
- **R²决定系数**：衡量模型拟合优度

### 特征说明
- `x1` - 地区生产总值
- `x3` - 固定资产投资
- `x4` - 社会消费品零售总额
- `x5` - 进出口总额
- `x6` - 年末总人口
- `x7` - 城镇化率
- `x8` - 人均可支配收入
- `x13` - 第三产业增加值

## 🎨 界面预览

```
┌─────────────────────────────────────────────────────────┐
│  [数据加载] [模型训练] [开始预测] [导出结果] [清除]        │
├─────────────┬─────────────────────┬─────────────────────┤
│  参数设置    │    结果显示区域      │    图表显示区域      │
│             │                    │                    │
│ 测试集比例   │  模型性能指标        │  [实际值vs预测值]   │
│ [====|====] │  MAPE: 0.xx%       │                    │
│             │                    │                    │
│ 预测年数     │  预测结果           │                    │
│ [  2  ]年   │  2024年: xxx万元    │                    │
│             │  2025年: xxx万元    │                    │
│ 模型参数     │                    │                    │
│ 系数: [...]  │  状态信息           │                    │
│ 截距: x.xx   │  ✓ 模型训练完成      │                    │
└─────────────┴─────────────────────┴─────────────────────┘
```

## 🔧 开发说明

### 代码规范
- 遵循PEP8编码规范
- 使用中文注释说明关键逻辑
- 模块化设计，便于维护和扩展

### 扩展功能
- 支持多种机器学习算法
- 增加更多评估指标
- 支持批量数据处理
- 添加数据预处理功能

## 📞 技术支持

如遇到问题或需要技术支持，请查看：
1. 项目文档和教程
2. 错误日志和提示信息
3. 联系开发团队

## 📄 许可证

本项目仅供学习和研究使用。

---

**开发团队**：辽宁对外经贸学院-大数据专业  
**项目类型**：财政收入预测分析系统  
**最后更新**：2025年8月
