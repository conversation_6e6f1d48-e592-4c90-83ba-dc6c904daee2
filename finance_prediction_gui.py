"""
财政收入预测分析系统 - GUI主程序
提供直观的图形界面进行财政收入预测分析
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import pandas as pd
import threading
from finance_model import FinancePredictionModel


class FinancePredictionGUI:
    """财政收入预测GUI主类"""
    
    def __init__(self):
        """初始化GUI界面"""
        self.root = tk.Tk()
        self.model = FinancePredictionModel()
        self.current_file = None
        
        # 设置窗口属性
        self.setup_window()
        
        # 创建界面组件
        self.create_widgets()
        
        # 设置样式
        self.setup_styles()
        
        # 绑定事件
        self.bind_events()
    
    def setup_window(self):
        """设置主窗口属性"""
        self.root.title("X市财政收入预测分析系统")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 设置窗口居中
        self.center_window()
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建所有GUI组件"""
        # 创建主框架
        self.create_main_frame()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建内容区域
        self.create_content_area()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_main_frame(self):
        """创建主框架"""
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar_frame = ttk.Frame(self.main_frame)
        self.toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 工具栏按钮
        self.btn_load_data = ttk.Button(
            self.toolbar_frame,
            text="📁 数据加载",
            command=self.load_data,
            style='Toolbar.TButton'
        )
        self.btn_load_data.pack(side=tk.LEFT, padx=(0, 8))

        self.btn_train_model = ttk.Button(
            self.toolbar_frame,
            text="🤖 模型训练",
            command=self.train_model,
            style='Toolbar.TButton',
            state=tk.DISABLED
        )
        self.btn_train_model.pack(side=tk.LEFT, padx=8)

        self.btn_predict = ttk.Button(
            self.toolbar_frame,
            text="🔮 开始预测",
            command=self.start_prediction,
            style='Toolbar.TButton',
            state=tk.DISABLED
        )
        self.btn_predict.pack(side=tk.LEFT, padx=8)

        self.btn_export = ttk.Button(
            self.toolbar_frame,
            text="💾 导出结果",
            command=self.export_results,
            style='Toolbar.TButton',
            state=tk.DISABLED
        )
        self.btn_export.pack(side=tk.LEFT, padx=8)

        self.btn_clear = ttk.Button(
            self.toolbar_frame,
            text="🗑️ 清除",
            command=self.clear_all,
            style='Toolbar.TButton'
        )
        self.btn_clear.pack(side=tk.LEFT, padx=8)

        # 添加分隔线
        separator = ttk.Separator(self.toolbar_frame, orient='vertical')
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=15)

        # 添加帮助按钮
        self.btn_help = ttk.Button(
            self.toolbar_frame,
            text="❓ 帮助",
            command=self.show_help,
            style='Toolbar.TButton'
        )
        self.btn_help.pack(side=tk.RIGHT, padx=8)
    
    def create_content_area(self):
        """创建内容区域"""
        # 创建主要内容框架
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧参数设置区域
        self.create_parameter_panel()
        
        # 中央结果显示区域
        self.create_results_panel()
        
        # 右侧图表显示区域
        self.create_chart_panel()
    
    def create_parameter_panel(self):
        """创建参数设置面板"""
        self.param_frame = ttk.LabelFrame(
            self.content_frame,
            text="⚙️ 参数设置",
            style='Custom.TLabelframe',
            padding=15
        )
        self.param_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 8))
        
        # 测试集比例设置
        ttk.Label(self.param_frame, text="测试集比例:").pack(anchor=tk.W, pady=(0, 5))
        self.test_size_var = tk.DoubleVar(value=0.1)
        self.test_size_scale = ttk.Scale(
            self.param_frame, 
            from_=0.05, 
            to=0.3, 
            variable=self.test_size_var,
            orient=tk.HORIZONTAL,
            length=150
        )
        self.test_size_scale.pack(anchor=tk.W, pady=(0, 5))
        
        self.test_size_label = ttk.Label(self.param_frame, text="10%")
        self.test_size_label.pack(anchor=tk.W, pady=(0, 15))
        
        # 预测年数设置
        ttk.Label(self.param_frame, text="预测年数:").pack(anchor=tk.W, pady=(0, 5))
        self.predict_years_var = tk.IntVar(value=2)
        self.predict_years_spinbox = ttk.Spinbox(
            self.param_frame,
            from_=1,
            to=5,
            textvariable=self.predict_years_var,
            width=10
        )
        self.predict_years_spinbox.pack(anchor=tk.W, pady=(0, 15))
        
        # 模型参数显示
        ttk.Label(self.param_frame, text="模型参数:", font=('Arial', 10, 'bold')).pack(anchor=tk.W, pady=(10, 5))
        
        self.param_text = tk.Text(
            self.param_frame, 
            height=8, 
            width=25,
            font=('Consolas', 9),
            state=tk.DISABLED
        )
        self.param_text.pack(anchor=tk.W, pady=(0, 10))
        
        # 滚动条
        param_scrollbar = ttk.Scrollbar(self.param_frame, orient=tk.VERTICAL, command=self.param_text.yview)
        self.param_text.configure(yscrollcommand=param_scrollbar.set)
    
    def create_results_panel(self):
        """创建结果显示面板"""
        self.results_frame = ttk.LabelFrame(
            self.content_frame,
            text="📊 结果显示",
            style='Custom.TLabelframe',
            padding=15
        )
        self.results_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8)
        
        # 模型性能指标
        ttk.Label(self.results_frame, text="模型性能指标:", font=('Arial', 11, 'bold')).pack(anchor=tk.W, pady=(0, 10))
        
        self.metrics_frame = ttk.Frame(self.results_frame)
        self.metrics_frame.pack(fill=tk.X, pady=(0, 15))
        
        # MAPE指标
        ttk.Label(self.metrics_frame, text="MAPE:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.mape_label = ttk.Label(self.metrics_frame, text="--", foreground="blue")
        self.mape_label.grid(row=0, column=1, sticky=tk.W)
        
        # R²指标
        ttk.Label(self.metrics_frame, text="R²:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.r2_label = ttk.Label(self.metrics_frame, text="--", foreground="blue")
        self.r2_label.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        # 预测结果
        ttk.Label(self.results_frame, text="预测结果:", font=('Arial', 11, 'bold')).pack(anchor=tk.W, pady=(15, 10))
        
        self.prediction_text = tk.Text(
            self.results_frame,
            height=6,
            width=40,
            font=('Arial', 10),
            state=tk.DISABLED
        )
        self.prediction_text.pack(fill=tk.X, pady=(0, 15))
        
        # 状态信息
        ttk.Label(self.results_frame, text="状态信息:", font=('Arial', 11, 'bold')).pack(anchor=tk.W, pady=(15, 10))
        
        self.status_text = tk.Text(
            self.results_frame,
            height=8,
            width=40,
            font=('Arial', 9),
            state=tk.DISABLED
        )
        self.status_text.pack(fill=tk.BOTH, expand=True)
    
    def create_chart_panel(self):
        """创建图表显示面板"""
        self.chart_frame = ttk.LabelFrame(
            self.content_frame,
            text="📈 数据可视化",
            style='Custom.TLabelframe',
            padding=15
        )
        self.chart_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(8, 0))
        
        # 创建matplotlib图表
        self.figure = Figure(figsize=(6, 6), dpi=100)
        self.ax = self.figure.add_subplot(111)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.canvas = FigureCanvasTkAgg(self.figure, self.chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 初始化空图表
        self.init_empty_chart()
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(self.status_bar, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
        
        # 进度条
        self.progress = ttk.Progressbar(self.status_bar, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=5, pady=2)
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()

        # 设置主题
        try:
            style.theme_use('clam')
        except:
            pass

        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))

        # 工具栏按钮样式
        style.configure('Toolbar.TButton',
                       font=('Arial', 10),
                       padding=(10, 5))

        # 标签框样式
        style.configure('Custom.TLabelframe',
                       borderwidth=2,
                       relief='groove')

        # 标签框标题样式
        style.configure('Custom.TLabelframe.Label',
                       font=('Arial', 11, 'bold'),
                       foreground='#2E86AB')

        # 设置颜色主题
        self.colors = {
            'primary': '#2E86AB',      # 主色调 - 蓝色
            'secondary': '#A23B72',    # 次色调 - 紫红色
            'success': '#F18F01',      # 成功色 - 橙色
            'warning': '#C73E1D',      # 警告色 - 红色
            'background': '#F5F5F5',   # 背景色 - 浅灰
            'text': '#333333'          # 文字色 - 深灰
        }

        # 配置根窗口背景
        self.root.configure(bg=self.colors['background'])
    
    def bind_events(self):
        """绑定事件"""
        # 测试集比例变化事件
        self.test_size_var.trace('w', self.on_test_size_change)
        
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def on_test_size_change(self, *args):
        """测试集比例变化时更新标签"""
        value = self.test_size_var.get()
        self.test_size_label.config(text=f"{value:.0%}")
    
    def init_empty_chart(self):
        """初始化空图表"""
        self.ax.clear()
        self.ax.set_title("财政收入预测对比图", fontsize=14, fontweight='bold')
        self.ax.set_xlabel("年份")
        self.ax.set_ylabel("财政收入 (万元)")
        self.ax.grid(True, alpha=0.3)
        self.ax.text(0.5, 0.5, '请先加载数据并训练模型', 
                    transform=self.ax.transAxes, 
                    ha='center', va='center',
                    fontsize=12, alpha=0.6)
        self.canvas.draw()
    
    def update_status(self, message: str):
        """更新状态栏信息"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def add_status_message(self, message: str):
        """添加状态信息到状态文本框"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
        self.root.update_idletasks()
    
    def load_data(self):
        """加载数据文件"""
        try:
            # 打开文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择财政数据文件",
                filetypes=[
                    ("CSV文件", "*.csv"),
                    ("Excel文件", "*.xlsx"),
                    ("所有文件", "*.*")
                ],
                initialdir="."
            )

            if not file_path:
                return

            # 更新状态
            self.update_status("正在加载数据...")
            self.progress.start()

            # 在后台线程中加载数据
            def load_in_background():
                try:
                    success = self.model.load_data(file_path)

                    # 在主线程中更新UI
                    self.root.after(0, self._on_data_loaded, success, file_path)

                except Exception as e:
                    self.root.after(0, self._on_data_load_error, str(e))

            threading.Thread(target=load_in_background, daemon=True).start()

        except Exception as e:
            self.progress.stop()
            self.update_status("就绪")
            messagebox.showerror("错误", f"加载数据时发生错误：{str(e)}")

    def _on_data_loaded(self, success: bool, file_path: str):
        """数据加载完成回调"""
        self.progress.stop()

        if success:
            self.current_file = file_path
            self.update_status(f"数据加载成功: {file_path}")
            self.add_status_message(f"✓ 数据文件加载成功: {file_path}")

            # 显示数据信息
            if self.model.data is not None:
                rows, cols = self.model.data.shape
                self.add_status_message(f"  数据维度: {rows} 行 × {cols} 列")
                self.add_status_message(f"  特征数量: {len(self.model.feature_names)}")
                self.add_status_message(f"  样本数量: {len(self.model.X)} (用于训练)")

            # 启用训练按钮
            self.btn_train_model.config(state=tk.NORMAL)

        else:
            self.update_status("数据加载失败")
            self.add_status_message("✗ 数据加载失败，请检查文件格式")
            messagebox.showerror("错误", "数据加载失败，请检查文件格式是否正确")

    def _on_data_load_error(self, error_msg: str):
        """数据加载错误回调"""
        self.progress.stop()
        self.update_status("数据加载失败")
        self.add_status_message(f"✗ 数据加载错误: {error_msg}")
        messagebox.showerror("错误", f"数据加载失败：{error_msg}")

    def train_model(self):
        """训练模型"""
        try:
            if self.model.data is None:
                messagebox.showwarning("警告", "请先加载数据文件")
                return

            # 更新状态
            self.update_status("正在训练模型...")
            self.progress.start()
            self.add_status_message("开始训练模型...")

            # 在后台线程中训练模型
            def train_in_background():
                try:
                    # 获取参数
                    test_size = self.test_size_var.get()

                    # 分割数据
                    split_success = self.model.split_data(test_size=test_size)
                    if not split_success:
                        self.root.after(0, self._on_train_error, "数据分割失败")
                        return

                    # 训练模型
                    train_success = self.model.train_model()
                    if not train_success:
                        self.root.after(0, self._on_train_error, "模型训练失败")
                        return

                    # 评估模型
                    metrics = self.model.evaluate_model()

                    # 在主线程中更新UI
                    self.root.after(0, self._on_model_trained, metrics)

                except Exception as e:
                    self.root.after(0, self._on_train_error, str(e))

            threading.Thread(target=train_in_background, daemon=True).start()

        except Exception as e:
            self.progress.stop()
            self.update_status("就绪")
            messagebox.showerror("错误", f"训练模型时发生错误：{str(e)}")

    def _on_model_trained(self, metrics: dict):
        """模型训练完成回调"""
        self.progress.stop()
        self.update_status("模型训练完成")
        self.add_status_message("✓ 模型训练完成")

        if metrics:
            # 更新性能指标显示
            self.mape_label.config(text=f"{metrics['mape']:.4f} ({metrics['mape']*100:.2f}%)")
            self.r2_label.config(text=f"{metrics['r2_score']:.4f}")

            self.add_status_message(f"  MAPE: {metrics['mape']*100:.2f}%")
            self.add_status_message(f"  R²: {metrics['r2_score']:.4f}")

            # 更新模型参数显示
            self._update_model_parameters()

            # 更新图表
            self._update_chart()

            # 启用预测按钮
            self.btn_predict.config(state=tk.NORMAL)
            self.btn_export.config(state=tk.NORMAL)

    def _on_train_error(self, error_msg: str):
        """模型训练错误回调"""
        self.progress.stop()
        self.update_status("模型训练失败")
        self.add_status_message(f"✗ 模型训练失败: {error_msg}")
        messagebox.showerror("错误", f"模型训练失败：{error_msg}")

    def start_prediction(self):
        """开始预测"""
        try:
            if not self.model.is_trained:
                messagebox.showwarning("警告", "请先训练模型")
                return

            # 更新状态
            self.update_status("正在进行预测...")
            self.progress.start()
            self.add_status_message("开始预测分析...")

            # 在后台线程中进行预测
            def predict_in_background():
                try:
                    # 获取预测年数
                    years = self.predict_years_var.get()

                    # 进行未来预测
                    future_predictions = self.model.predict_future(years)

                    if future_predictions is None:
                        self.root.after(0, self._on_predict_error, "预测失败")
                        return

                    # 在主线程中更新UI
                    self.root.after(0, self._on_prediction_complete, future_predictions, years)

                except Exception as e:
                    self.root.after(0, self._on_predict_error, str(e))

            threading.Thread(target=predict_in_background, daemon=True).start()

        except Exception as e:
            self.progress.stop()
            self.update_status("就绪")
            messagebox.showerror("错误", f"预测时发生错误：{str(e)}")

    def _on_prediction_complete(self, predictions, years):
        """预测完成回调"""
        self.progress.stop()
        self.update_status("预测完成")
        self.add_status_message("✓ 预测分析完成")

        # 更新预测结果显示
        self.prediction_text.config(state=tk.NORMAL)
        self.prediction_text.delete(1.0, tk.END)

        self.prediction_text.insert(tk.END, "未来财政收入预测结果:\n\n")

        # 获取当前年份（假设数据最后一年为基准）
        base_year = 2022  # 可以根据实际数据调整

        for i, pred in enumerate(predictions):
            year = base_year + i + 1
            self.prediction_text.insert(tk.END, f"{year}年: {pred:.2f} 万元\n")
            self.add_status_message(f"  {year}年预测: {pred:.2f} 万元")

        # 计算增长率
        if len(predictions) > 1:
            growth_rate = ((predictions[-1] - predictions[0]) / predictions[0]) * 100
            self.prediction_text.insert(tk.END, f"\n预测期间平均增长率: {growth_rate:.2f}%")
            self.add_status_message(f"  平均增长率: {growth_rate:.2f}%")

        self.prediction_text.config(state=tk.DISABLED)

        # 更新图表，添加预测点
        self._update_chart_with_predictions(predictions, years)

    def _on_predict_error(self, error_msg: str):
        """预测错误回调"""
        self.progress.stop()
        self.update_status("预测失败")
        self.add_status_message(f"✗ 预测失败: {error_msg}")
        messagebox.showerror("错误", f"预测失败：{error_msg}")

    def _update_chart_with_predictions(self, predictions, years):
        """更新图表，添加预测点"""
        plot_data = self.model.get_prediction_data()
        if plot_data:
            self.ax.clear()

            # 绘制历史数据
            hist_years = plot_data['years']
            actual = plot_data['actual_values']
            predicted = plot_data['predicted_values']

            self.ax.plot(hist_years, actual, 'b-o', label='实际值', linewidth=2, markersize=6)
            self.ax.plot(hist_years, predicted, 'r-*', label='历史预测值', linewidth=2, markersize=6)

            # 绘制未来预测
            base_year = max(hist_years)
            future_years = [base_year + i + 1 for i in range(years)]
            self.ax.plot(future_years, predictions, 'g-s', label='未来预测值',
                        linewidth=2, markersize=8, alpha=0.8)

            # 连接线
            if len(hist_years) > 0 and len(predictions) > 0:
                self.ax.plot([hist_years[-1], future_years[0]],
                           [predicted[-1], predictions[0]],
                           'g--', alpha=0.5, linewidth=1)

            self.ax.set_title("财政收入预测对比图", fontsize=14, fontweight='bold')
            self.ax.set_xlabel("年份")
            self.ax.set_ylabel("财政收入 (万元)")
            self.ax.legend()
            self.ax.grid(True, alpha=0.3)

            # 美化图表
            self.ax.spines['top'].set_visible(False)
            self.ax.spines['right'].set_visible(False)

            self.figure.tight_layout()
            self.canvas.draw()

    def export_results(self):
        """导出结果"""
        try:
            if not self.model.is_trained:
                messagebox.showwarning("警告", "请先训练模型并进行预测")
                return

            # 选择保存位置
            file_path = filedialog.asksaveasfilename(
                title="保存预测结果",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel文件", "*.xlsx"),
                    ("CSV文件", "*.csv"),
                    ("所有文件", "*.*")
                ]
            )

            if not file_path:
                return

            self.update_status("正在导出结果...")
            self.progress.start()

            # 在后台线程中导出
            def export_in_background():
                try:
                    # 准备导出数据
                    export_data = self._prepare_export_data()

                    if file_path.endswith('.xlsx'):
                        # 导出到Excel
                        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                            # 模型性能
                            metrics_df = pd.DataFrame([export_data['metrics']])
                            metrics_df.to_excel(writer, sheet_name='模型性能', index=False)

                            # 历史数据和预测
                            history_df = pd.DataFrame(export_data['history'])
                            history_df.to_excel(writer, sheet_name='历史数据', index=False)

                            # 未来预测
                            if export_data['future_predictions']:
                                future_df = pd.DataFrame(export_data['future_predictions'])
                                future_df.to_excel(writer, sheet_name='未来预测', index=False)

                            # 模型参数
                            params_df = pd.DataFrame(export_data['model_params'])
                            params_df.to_excel(writer, sheet_name='模型参数', index=False)

                    else:
                        # 导出到CSV（合并所有数据）
                        all_data = []
                        all_data.extend(export_data['history'])
                        if export_data['future_predictions']:
                            all_data.extend(export_data['future_predictions'])

                        df = pd.DataFrame(all_data)
                        df.to_csv(file_path, index=False, encoding='utf-8-sig')

                    self.root.after(0, self._on_export_complete, file_path)

                except Exception as e:
                    self.root.after(0, self._on_export_error, str(e))

            threading.Thread(target=export_in_background, daemon=True).start()

        except Exception as e:
            self.progress.stop()
            self.update_status("就绪")
            messagebox.showerror("错误", f"导出时发生错误：{str(e)}")

    def _prepare_export_data(self):
        """准备导出数据"""
        export_data = {
            'metrics': {},
            'history': [],
            'future_predictions': [],
            'model_params': []
        }

        # 模型性能指标
        metrics = self.model.evaluate_model()
        if metrics:
            export_data['metrics'] = {
                'MAPE': f"{metrics['mape']*100:.2f}%",
                'R²': f"{metrics['r2_score']:.4f}",
                'MSE': f"{metrics['mse']:.4f}",
                'MAE': f"{metrics['mae']:.4f}"
            }

        # 历史数据
        plot_data = self.model.get_prediction_data()
        if plot_data:
            for i, (year, actual, predicted) in enumerate(zip(
                plot_data['years'],
                plot_data['actual_values'],
                plot_data['predicted_values']
            )):
                export_data['history'].append({
                    '年份': year,
                    '实际值': f"{actual:.2f}",
                    '预测值': f"{predicted:.2f}",
                    '误差': f"{abs(actual - predicted):.2f}",
                    '误差率': f"{abs(actual - predicted)/actual*100:.2f}%"
                })

        # 模型参数
        params = self.model.get_model_parameters()
        if params:
            for feature, coef in zip(params['feature_names'], params['coefficients']):
                export_data['model_params'].append({
                    '特征': feature,
                    '系数': f"{coef:.6f}"
                })
            export_data['model_params'].append({
                '特征': '截距',
                '系数': f"{params['intercept']:.6f}"
            })

        return export_data

    def _on_export_complete(self, file_path):
        """导出完成回调"""
        self.progress.stop()
        self.update_status("导出完成")
        self.add_status_message(f"✓ 结果已导出到: {file_path}")
        messagebox.showinfo("成功", f"预测结果已成功导出到:\n{file_path}")

    def _on_export_error(self, error_msg):
        """导出错误回调"""
        self.progress.stop()
        self.update_status("导出失败")
        self.add_status_message(f"✗ 导出失败: {error_msg}")
        messagebox.showerror("错误", f"导出失败：{error_msg}")

    def clear_all(self):
        """清除所有数据"""
        # 重置模型
        self.model.reset_model()
        self.current_file = None

        # 清除显示内容
        self.mape_label.config(text="--")
        self.r2_label.config(text="--")

        # 清除文本框
        self.param_text.config(state=tk.NORMAL)
        self.param_text.delete(1.0, tk.END)
        self.param_text.config(state=tk.DISABLED)

        self.prediction_text.config(state=tk.NORMAL)
        self.prediction_text.delete(1.0, tk.END)
        self.prediction_text.config(state=tk.DISABLED)

        self.status_text.config(state=tk.NORMAL)
        self.status_text.delete(1.0, tk.END)
        self.status_text.config(state=tk.DISABLED)

        # 重置图表
        self.init_empty_chart()

        # 禁用按钮
        self.btn_train_model.config(state=tk.DISABLED)
        self.btn_predict.config(state=tk.DISABLED)
        self.btn_export.config(state=tk.DISABLED)

        # 更新状态
        self.update_status("已清除所有数据")
        self.add_status_message("✓ 已清除所有数据")

    def _update_model_parameters(self):
        """更新模型参数显示"""
        params = self.model.get_model_parameters()
        if params:
            self.param_text.config(state=tk.NORMAL)
            self.param_text.delete(1.0, tk.END)

            # 显示系数
            self.param_text.insert(tk.END, "模型系数:\n")
            for i, (feature, coef) in enumerate(zip(params['feature_names'], params['coefficients'])):
                self.param_text.insert(tk.END, f"{feature}: {coef:.4f}\n")

            # 显示截距
            self.param_text.insert(tk.END, f"\n截距: {params['intercept']:.4f}")

            self.param_text.config(state=tk.DISABLED)

    def _update_chart(self):
        """更新图表显示"""
        plot_data = self.model.get_prediction_data()
        if plot_data:
            self.ax.clear()

            # 绘制实际值和预测值
            years = plot_data['years']
            actual = plot_data['actual_values']
            predicted = plot_data['predicted_values']

            self.ax.plot(years, actual, 'b-o', label='实际值', linewidth=2, markersize=6)
            self.ax.plot(years, predicted, 'r-*', label='预测值', linewidth=2, markersize=8)

            self.ax.set_title("财政收入预测对比图", fontsize=14, fontweight='bold')
            self.ax.set_xlabel("年份")
            self.ax.set_ylabel("财政收入 (万元)")
            self.ax.legend()
            self.ax.grid(True, alpha=0.3)

            # 美化图表
            self.ax.spines['top'].set_visible(False)
            self.ax.spines['right'].set_visible(False)

            self.figure.tight_layout()
            self.canvas.draw()
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
X市财政收入预测分析系统 - 使用帮助

📋 操作流程：
1. 点击"数据加载"选择CSV数据文件
2. 调整参数设置（测试集比例、预测年数）
3. 点击"模型训练"训练预测模型
4. 点击"开始预测"进行未来预测
5. 点击"导出结果"保存分析结果

📊 数据格式要求：
• CSV文件包含特征列：x1, x3, x4, x5, x6, x7, x8, x13
• 目标列：y（财政收入）
• 数据应为数值型，无缺失值

⚙️ 参数说明：
• 测试集比例：用于模型验证的数据比例（5%-30%）
• 预测年数：预测未来的年数（1-5年）

📈 评估指标：
• MAPE：平均绝对百分比误差，越小越好
• R²：决定系数，越接近1越好

💡 使用技巧：
• 建议测试集比例设为10%-20%
• 数据质量直接影响预测准确性
• 可多次调整参数对比效果

如有问题，请检查数据格式或联系技术支持。
        """

        # 创建帮助窗口
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.resizable(False, False)

        # 居中显示
        help_window.transient(self.root)
        help_window.grab_set()

        # 创建文本框显示帮助内容
        text_frame = ttk.Frame(help_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        help_text_widget = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=('Arial', 10),
            bg='#F8F9FA',
            relief=tk.FLAT,
            padx=15,
            pady=15
        )
        help_text_widget.pack(fill=tk.BOTH, expand=True)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=help_text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        help_text_widget.configure(yscrollcommand=scrollbar.set)

        # 插入帮助文本
        help_text_widget.insert(tk.END, help_text)
        help_text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        btn_frame = ttk.Frame(help_window)
        btn_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        close_btn = ttk.Button(
            btn_frame,
            text="关闭",
            command=help_window.destroy,
            style='Toolbar.TButton'
        )
        close_btn.pack(side=tk.RIGHT)

    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            self.root.destroy()

    def run(self):
        """运行GUI程序"""
        try:
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("严重错误", f"程序运行时发生严重错误：{str(e)}")
            print(f"GUI运行错误: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    app = FinancePredictionGUI()
    app.run()
