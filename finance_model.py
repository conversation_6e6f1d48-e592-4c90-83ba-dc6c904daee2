"""
财政收入预测模型类
封装了数据处理、模型训练、预测和评估的核心逻辑
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt
import warnings
from typing import Tuple, Optional, Dict, Any

warnings.filterwarnings('ignore')


class FinancePredictionModel:
    """财政收入预测模型类"""
    
    def __init__(self):
        """初始化模型"""
        self.model = LinearRegression()
        self.data = None
        self.X = None
        self.y = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.is_trained = False
        self.feature_names = ['x1', 'x3', 'x4', 'x5', 'x6', 'x7', 'x8', 'x13']
        self.target_name = 'y'
        
    def load_data(self, file_path: str) -> bool:
        """
        加载数据文件
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            self.data = pd.read_csv(file_path, index_col=0)
            
            # 验证数据格式
            if not self._validate_data():
                return False
                
            # 准备特征和目标变量
            self.X = self.data.iloc[:-2, :-1]  # 除最后两行外的所有特征
            self.y = self.data.iloc[:-2, -1]   # 除最后两行外的目标变量
            
            return True
            
        except Exception as e:
            print(f"数据加载失败: {str(e)}")
            return False
    
    def _validate_data(self) -> bool:
        """
        验证数据格式和完整性
        
        Returns:
            bool: 验证是否通过
        """
        if self.data is None:
            return False
            
        # 检查必要的列是否存在
        required_columns = self.feature_names + [self.target_name]
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        
        if missing_columns:
            print(f"缺少必要的列: {missing_columns}")
            return False
            
        # 检查数据是否有足够的行数
        if len(self.data) < 5:
            print("数据行数不足，至少需要5行数据")
            return False
            
        # 检查是否有缺失值
        if self.data.isnull().any().any():
            print("数据中存在缺失值")
            return False
            
        return True
    
    def split_data(self, test_size: float = 0.1, random_state: int = 42) -> bool:
        """
        分割训练集和测试集
        
        Args:
            test_size: 测试集比例
            random_state: 随机种子
            
        Returns:
            bool: 分割是否成功
        """
        try:
            if self.X is None or self.y is None:
                print("请先加载数据")
                return False
                
            self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
                self.X, self.y, test_size=test_size, random_state=random_state
            )
            return True
            
        except Exception as e:
            print(f"数据分割失败: {str(e)}")
            return False
    
    def train_model(self) -> bool:
        """
        训练模型
        
        Returns:
            bool: 训练是否成功
        """
        try:
            if self.X_train is None or self.y_train is None:
                print("请先分割数据")
                return False
                
            self.model.fit(self.X_train, self.y_train)
            self.is_trained = True
            return True
            
        except Exception as e:
            print(f"模型训练失败: {str(e)}")
            return False
    
    def predict(self, X_input: Optional[pd.DataFrame] = None) -> Optional[np.ndarray]:
        """
        进行预测
        
        Args:
            X_input: 输入特征，如果为None则使用测试集
            
        Returns:
            预测结果数组
        """
        try:
            if not self.is_trained:
                print("请先训练模型")
                return None
                
            if X_input is None:
                X_input = self.X_test
                
            return self.model.predict(X_input)
            
        except Exception as e:
            print(f"预测失败: {str(e)}")
            return None
    
    def predict_future(self, years: int = 2) -> Optional[np.ndarray]:
        """
        预测未来年份的财政收入
        
        Args:
            years: 预测年数
            
        Returns:
            未来年份的预测结果
        """
        try:
            if not self.is_trained:
                print("请先训练模型")
                return None
                
            if self.data is None:
                print("请先加载数据")
                return None
                
            # 获取最后几年的数据作为预测输入
            X_future = self.data.iloc[-years:, :-1]
            return self.model.predict(X_future)
            
        except Exception as e:
            print(f"未来预测失败: {str(e)}")
            return None
    
    def evaluate_model(self) -> Optional[Dict[str, float]]:
        """
        评估模型性能
        
        Returns:
            包含评估指标的字典
        """
        try:
            if not self.is_trained:
                print("请先训练模型")
                return None
                
            y_pred = self.predict()
            if y_pred is None:
                return None
                
            # 计算MAPE
            mape = self._calculate_mape(self.y_test, y_pred)
            
            # 计算R²
            r2_score = self.model.score(self.X_test, self.y_test)
            
            return {
                'mape': mape,
                'r2_score': r2_score,
                'mse': np.mean((self.y_test - y_pred) ** 2),
                'mae': np.mean(np.abs(self.y_test - y_pred))
            }
            
        except Exception as e:
            print(f"模型评估失败: {str(e)}")
            return None
    
    def _calculate_mape(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算平均绝对百分比误差
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            MAPE值
        """
        return np.mean(np.abs((y_pred - y_true) / y_true))
    
    def get_model_parameters(self) -> Optional[Dict[str, Any]]:
        """
        获取模型参数
        
        Returns:
            模型参数字典
        """
        if not self.is_trained:
            return None
            
        return {
            'coefficients': self.model.coef_.tolist(),
            'intercept': self.model.intercept_,
            'feature_names': self.feature_names
        }
    
    def get_prediction_data(self) -> Optional[Dict[str, Any]]:
        """
        获取用于绘图的预测数据
        
        Returns:
            包含实际值和预测值的字典
        """
        try:
            if not self.is_trained or self.data is None:
                return None
                
            # 对所有历史数据进行预测
            y_all_pred = self.model.predict(self.X)
            
            # 获取实际值（历史数据）
            y_actual = self.y.values
            
            return {
                'actual_values': y_actual,
                'predicted_values': y_all_pred,
                'years': list(range(1, len(y_actual) + 1))
            }
            
        except Exception as e:
            print(f"获取预测数据失败: {str(e)}")
            return None
    
    def reset_model(self):
        """重置模型状态"""
        self.model = LinearRegression()
        self.data = None
        self.X = None
        self.y = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.is_trained = False


# 示例使用
if __name__ == "__main__":
    # 创建模型实例
    model = FinancePredictionModel()
    
    # 加载数据
    if model.load_data('finance_data.csv'):
        print("数据加载成功")
        
        # 分割数据
        if model.split_data(test_size=0.1):
            print("数据分割成功")
            
            # 训练模型
            if model.train_model():
                print("模型训练成功")
                
                # 评估模型
                metrics = model.evaluate_model()
                if metrics:
                    print(f"模型性能: MAPE = {metrics['mape']:.4f}")
                
                # 预测未来
                future_pred = model.predict_future(2)
                if future_pred is not None:
                    print(f"未来预测结果: {future_pred}")
